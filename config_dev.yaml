# 开发环境配置文件
# 用于开发和测试环境的特定配置

# 日志配置 - 开发环境使用更详细的日志
logging:
  level: "DEBUG"
  format: "%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  use_colors: true
  enable_file_logging: true
  file_log_level: "DEBUG"
  enable_masking: false  # 开发环境不需要数据脱敏

# 服务配置 - 开发环境端口
monitor_service:
  host: "127.0.0.1"
  port: 8080
  startup_delay: 1  # 开发环境减少启动延迟
  max_startup_wait: 20

web_service:
  host: "0.0.0.0"
  port: 5001
  startup_delay: 1  # 开发环境减少启动延迟
  max_startup_wait: 30

# 开发环境特定功能
development:
  debug_mode: true
  hot_reload: true
  verbose_logging: true

# API错误监控 - 开发环境更敏感的阈值
api_error_monitoring:
  consecutive_error_threshold: 300   # 开发环境更快触发告警
  error_duration_threshold: 200     # 30秒
  notification_interval: 60        # 1分钟
  recovery_notification: true