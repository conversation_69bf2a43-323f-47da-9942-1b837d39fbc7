#!/usr/bin/env python3
"""
青城住房监控系统 - 简化版一键启动程序
轻量级启动脚本，快速启动监控服务和Web控制台
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.unified_config import get_config


class SimpleStarter:
    """简化启动器"""

    def __init__(self):
        self.processes = []
        self.shutdown_requested = False

        # 获取统一配置
        self.unified_config = get_config()
        self.monitor_config = self.unified_config.get_monitor_service_config()
        self.web_config = self.unified_config.get_web_service_config()

    def log(self, message):
        """简单日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
        print(f"[{timestamp}] {message}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.log("收到停止信号，正在关闭所有服务...")
            self.shutdown_requested = True
            self.stop_all()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)

    def start_service(self, script_name, service_name):
        """启动单个服务"""
        self.log(f"正在启动 {service_name}...")

        script_path = Path(__file__).parent / script_name
        if not script_path.exists():
            self.log(f"错误: 启动脚本 {script_name} 不存在")
            return None

        try:
            process = subprocess.Popen([sys.executable, str(script_path)])
            self.processes.append((process, service_name))
            self.log(f"{service_name} 启动成功，PID: {process.pid}")
            return process
        except Exception as e:
            self.log(f"启动 {service_name} 失败: {str(e)}")
            return None

    def stop_all(self):
        """停止所有服务"""
        self.log("正在停止所有服务...")

        for process, service_name in self.processes:
            try:
                if process.poll() is None:  # 进程还在运行
                    self.log(f"正在停止 {service_name}...")
                    process.terminate()
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        self.log(f"强制结束 {service_name}")
                        process.kill()
            except Exception as e:
                self.log(f"停止 {service_name} 时出错: {str(e)}")

        self.log("所有服务已停止")

    def run(self):
        """运行启动器"""
        self.setup_signal_handlers()

        print("="*60)
        print("🏠 青城住房监控系统 - 简化版一键启动")
        print("="*60)

        # 启动监控服务
        monitor_process = self.start_service('start_monitor_service.py', '监控服务')
        if not monitor_process:
            self.log("监控服务启动失败，退出")
            return

        # 等待监控服务启动
        monitor_startup_delay = self.monitor_config.get('startup_delay', 3)
        self.log(f"等待监控服务启动（{monitor_startup_delay}秒）...")
        time.sleep(monitor_startup_delay)

        # 启动Web控制台
        web_process = self.start_service('web/app.py', 'Web控制台')
        if not web_process:
            self.log("Web控制台启动失败，但监控服务将继续运行")

        print("="*60)
        print("🎉 启动完成！")
        print(f"📊 监控服务: http://{self.monitor_config['host']}:{self.monitor_config['port']}")
        print(f"🌐 Web控制台: http://{self.web_config['host']}:{self.web_config['port']}")
        print("💡 按 Ctrl+C 停止所有服务")
        print("="*60)

        # 等待用户中断
        try:
            while not self.shutdown_requested:
                # 检查进程状态
                for process, service_name in self.processes:
                    if process.poll() is not None:
                        self.log(f"警告: {service_name} 意外退出")

                # 使用配置的检查间隔
                check_interval = self.unified_config.get('service_management.check_interval', 5)
                time.sleep(check_interval)
        except KeyboardInterrupt:
            self.log("收到中断信号")
        finally:
            self.stop_all()


def main():
    """主函数"""
    starter = SimpleStarter()
    starter.run()


if __name__ == "__main__":
    main()