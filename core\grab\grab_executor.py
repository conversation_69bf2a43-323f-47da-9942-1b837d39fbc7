"""
抢房执行器 - 集成自动抢房流程
"""

import os
import json
import asyncio
import random
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import re

# 导入登录和抢房相关模块
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'ZDQF'))

try:
    from ZDQF.login import HuhhothomeLogin, run_application_for_profile
    LOGIN_AVAILABLE = True
except ImportError:
    LOGIN_AVAILABLE = False


class GrabExecutor:
    """抢房执行器类"""

    def __init__(self, logger, data_manager=None, proxy_config=None, enable_debug=False, proxy_manager=None):
        self.logger = logger
        self.data_manager = data_manager
        self.proxy_config = proxy_config
        self.proxy_manager = proxy_manager  # 集成代理管理器
        self.enable_debug = enable_debug  # 调试开关，默认关闭以提升性能
        self.profiles_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'ZDQF', 'profiles')

        # 只有启用调试时才创建调试目录
        if self.enable_debug:
            self.debug_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'debug')
            os.makedirs(self.debug_dir, exist_ok=True)
        else:
            self.debug_dir = None

    async def execute_grab(self, device: Dict[str, Any], house_id: str, estate_name: str) -> Dict[str, Any]:
        """
        执行抢房操作

        Args:
            device: 抢房设备信息
            house_id: 房源ID
            estate_name: 小区名称

        Returns:
            Dict: 抢房结果 {'success': bool, 'message': str, 'details': str}
        """
        step_start_time = datetime.now()

        try:
            self.logger.info(f" ========== GrabExecutor 抢房流程启动 ==========")
            self.logger.info(f" 流程开始时间: {step_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

            if not LOGIN_AVAILABLE:
                self.logger.error(f"❌ 步骤失败: 抢房模块不可用，login.py文件导入失败")
                return {
                    'success': False,
                    'message': '抢房模块不可用，login.py文件导入失败',
                    'details': '请检查ZDQF/login.py文件是否存在'
                }

            username = device.get('username')
            if not username:
                self.logger.error(f"❌ 步骤失败: 设备信息缺少用户名")
                return {
                    'success': False,
                    'message': '设备信息缺少用户名',
                    'details': '无法确定用户身份'
                }

            self.logger.info(f"👤 抢房用户: {username}")
            self.logger.info(f"🏘️ 目标小区: {estate_name}")
            self.logger.info(f"🆔 目标房源ID: {house_id}")

            # 1. 验证Cookie有效性
            self.logger.info(f"🔍 步骤1: 开始验证Cookie有效性...")
            step_time = datetime.now()
            cookie_valid = await self._validate_cookie(device)
            step_duration = (datetime.now() - step_time).total_seconds()

            if not cookie_valid:
                self.logger.error(f"❌ 步骤1失败: Cookie验证失败 (耗时: {step_duration:.2f}秒)")
                return {
                    'success': False,
                    'message': 'Cookie已失效，无法执行抢房',
                    'details': '请重新登录获取有效的Cookie'
                }
            else:
                self.logger.info(f"✅ 步骤1成功: Cookie验证通过 (耗时: {step_duration:.2f}秒)")

            # 2. 检查用户配置文件
            self.logger.info(f"📁 步骤2: 检查用户配置文件...")
            step_time = datetime.now()
            profile_dir = os.path.join(self.profiles_dir, username)
            step_duration = (datetime.now() - step_time).total_seconds()

            if not os.path.exists(profile_dir):
                self.logger.error(f"❌ 步骤2失败: 用户配置目录不存在 (路径: {profile_dir}) (耗时: {step_duration:.2f}秒)")
                return {
                    'success': False,
                    'message': f'用户配置目录不存在: {username}',
                    'details': f'路径: {profile_dir}'
                }
            else:
                self.logger.info(f"✅ 步骤2成功: 用户配置目录存在 (路径: {profile_dir}) (耗时: {step_duration:.2f}秒)")

            # 3. 准备抢房配置
            self.logger.info(f"⚙️ 步骤3: 准备抢房配置...")
            step_time = datetime.now()
            config_result = await self._prepare_grab_config(profile_dir, house_id)
            step_duration = (datetime.now() - step_time).total_seconds()

            if not config_result['success']:
                self.logger.error(f"❌ 步骤3失败: {config_result.get('message', '未知错误')} (耗时: {step_duration:.2f}秒)")
                return config_result
            else:
                self.logger.info(f"✅ 步骤3成功: 抢房配置准备完成 (耗时: {step_duration:.2f}秒)")

            # 4. 创建登录客户端并设置Cookie
            self.logger.info(f"🌐 步骤4: 创建登录客户端并设置Cookie...")
            step_time = datetime.now()
            login_client = self._create_login_client(device)
            step_duration = (datetime.now() - step_time).total_seconds()

            if not login_client:
                self.logger.error(f"❌ 步骤4失败: 创建登录客户端失败 (耗时: {step_duration:.2f}秒)")
                return {
                    'success': False,
                    'message': '创建登录客户端失败',
                    'details': 'HuhhothomeLogin初始化失败'
                }
            else:
                self.logger.info(f"✅ 步骤4成功: 登录客户端创建完成 (耗时: {step_duration:.2f}秒)")

            # 4.1. 获取用户信息以支持自动填充
            self.logger.info(f"👤 步骤4.1: 获取用户信息以支持自动填充...")
            step_time = datetime.now()
            try:
                user_info = login_client.get_user_info()
                step_duration = (datetime.now() - step_time).total_seconds()
                if user_info:
                    self.logger.info(f"✅ 步骤4.1成功: 成功获取用户 {username} 的信息，支持自动填充 (耗时: {step_duration:.2f}秒)")
                else:
                    self.logger.warning(f"⚠️ 步骤4.1警告: 无法获取用户 {username} 的信息，自动填充功能可能受限 (耗时: {step_duration:.2f}秒)")
            except Exception as e:
                step_duration = (datetime.now() - step_time).total_seconds()
                self.logger.warning(f"⚠️ 步骤4.1警告: 获取用户信息时出错: {e} (耗时: {step_duration:.2f}秒)")

            # 5. 执行抢房流程
            self.logger.info(f"🎯 步骤5: 开始执行核心抢房流程...")
            self.logger.info(f"📋 抢房详情: 用户 {username} → 房源 {house_id}")
            step_time = datetime.now()
            grab_result = await self._run_grab_process(login_client, profile_dir, house_id)
            step_duration = (datetime.now() - step_time).total_seconds()
            total_duration = (datetime.now() - step_start_time).total_seconds()

            if grab_result['success']:
                self.logger.info(f"✅ 步骤5成功: 抢房流程执行成功 (耗时: {step_duration:.2f}秒)")
                self.logger.info(f"🎉 ========== 抢房流程成功完成 ==========")
                self.logger.info(f"🏆 成功结果: 用户 {username} 成功抢到房源 {house_id}")
                self.logger.info(f"⏱️ 总耗时: {total_duration:.2f}秒")
                self.logger.info(f"============================================")
                return {
                    'success': True,
                    'message': f'抢房成功！房源ID: {house_id}',
                    'details': f'用户: {username}, 小区: {estate_name}, 房源: {house_id}, 总耗时: {total_duration:.2f}秒'
                }
            else:
                self.logger.warning(f"❌ 步骤5失败: 抢房流程执行失败 (耗时: {step_duration:.2f}秒)")
                self.logger.warning(f"💔 ========== 抢房流程失败结束 ==========")
                self.logger.warning(f"🚫 失败结果: 用户 {username} 抢房失败: {grab_result['message']}")
                self.logger.warning(f"⏱️ 总耗时: {total_duration:.2f}秒")
                self.logger.warning(f"===========================================")
                return {
                    'success': False,
                    'message': f'抢房失败: {grab_result["message"]}',
                    'details': grab_result.get('details', '') + f' (总耗时: {total_duration:.2f}秒)'
                }

        except Exception as e:
            total_duration = (datetime.now() - step_start_time).total_seconds()
            username = device.get("username", "未知")

            self.logger.error(f"💥 ========== 抢房流程异常终止 ==========")
            self.logger.error(f"⚠️ 异常时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.error(f"👤 用户: {username}")
            self.logger.error(f"🆔 房源ID: {house_id}")
            self.logger.error(f"🏘️ 小区: {estate_name}")
            self.logger.error(f"🔥 异常信息: {str(e)}")
            self.logger.error(f"📊 异常类型: {type(e).__name__}")
            self.logger.error(f"⏱️ 异常前耗时: {total_duration:.2f}秒")
            self.logger.error(f"==========================================")

            return {
                'success': False,
                'message': f'抢房执行异常: {str(e)}',
                'details': f'用户: {username}, 房源: {house_id}, 小区: {estate_name}, 异常类型: {type(e).__name__}, 耗时: {total_duration:.2f}秒'
            }

    async def _validate_cookie(self, device: Dict[str, Any]) -> bool:
        """验证Cookie有效性"""
        try:
            self.logger.info(f"🔍 Cookie验证: 开始检查Cookie和Access Token...")

            cookie_str = device.get('cookie')
            access_token = device.get('access_token')

            if not cookie_str:
                self.logger.warning(f"❌ Cookie验证: Cookie字符串为空")
                return False

            if not access_token:
                self.logger.warning(f"❌ Cookie验证: Access Token为空")
                return False

            self.logger.info(f"✅ Cookie验证: Cookie和Access Token存在")

            # 这里可以添加更详细的Cookie验证逻辑
            # 比如检查过期时间、验证token等
            cookie_expires_at = device.get('cookie_expires_at')
            if cookie_expires_at:
                self.logger.info(f"🕒 Cookie验证: 检查过期时间 {cookie_expires_at}")
                # 简单的过期时间检查
                from datetime import datetime
                try:
                    expires_at = datetime.fromisoformat(cookie_expires_at.replace('Z', '+00:00'))
                    if datetime.now() >= expires_at:
                        self.logger.warning(f"❌ Cookie验证: Cookie已过期 {cookie_expires_at}")
                        return False
                    else:
                        remaining_time = expires_at - datetime.now()
                        self.logger.info(f"✅ Cookie验证: Cookie未过期，剩余有效时间 {remaining_time}")
                except Exception as parse_e:
                    self.logger.warning(f"⚠️ Cookie验证: 解析过期时间失败，忽略过期检查: {parse_e}")
            else:
                self.logger.info(f"⚠️ Cookie验证: 未设置过期时间，跳过过期检查")

            self.logger.info(f"✅ Cookie验证: 所有检查通过")
            return True

        except Exception as e:
            self.logger.error(f"❌ Cookie验证: 验证Cookie时出错: {e}")
            return False

    async def _prepare_grab_config(self, profile_dir: str, house_id: str) -> Dict[str, Any]:
        """准备抢房配置"""
        try:
            self.logger.info(f"⚙️ 配置准备: 开始准备抢房配置...")
            config_path = os.path.join(profile_dir, "config.json")
            self.logger.info(f"📁 配置准备: 配置文件路径 {config_path}")

            if not os.path.exists(config_path):
                self.logger.error(f"❌ 配置准备: 配置文件不存在")
                return {
                    'success': False,
                    'message': '配置文件不存在',
                    'details': f'路径: {config_path}'
                }

            self.logger.info(f"✅ 配置准备: 配置文件存在，开始读取...")
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            self.logger.info(f"✅ 配置准备: 配置文件读取成功")
            self.logger.info(f"🎯 配置准备: 更新目标房源ID为 {house_id}")

            # 更新房源ID
            old_house_id = config_data.get('house_id', 'None')
            config_data['house_id'] = house_id
            self.logger.info(f"🔄 配置准备: 房源ID更新 {old_house_id} → {house_id}")

            # 验证必要的配置项
            self.logger.info(f"🔍 配置准备: 验证必要的配置项...")
            application_data = config_data.get("application_data")
            if not application_data:
                self.logger.error(f"❌ 配置准备: 配置文件缺少application_data")
                return {
                    'success': False,
                    'message': '配置文件缺少application_data',
                    'details': '请检查配置文件格式'
                }

            self.logger.info(f"✅ 配置准备: application_data 配置项存在")

            # 记录一些重要的配置信息（不包含敏感信息）
            if isinstance(application_data, dict):
                self.logger.info(f"📋 配置准备: 申请数据包含 {len(application_data)} 个字段")
                # 记录非敏感字段的存在情况
                safe_fields = ['name', 'phone', 'idcard', 'gender', 'age', 'profession']
                for field in safe_fields:
                    if field in application_data:
                        field_value = application_data[field]
                        if isinstance(field_value, str) and len(field_value) > 0:
                            # 对敏感字段进行脱敏显示
                            if field == 'phone':
                                display_value = field_value[:3] + '****' + field_value[-4:] if len(field_value) >= 7 else '****'
                            elif field == 'idcard':
                                display_value = field_value[:4] + '****' + field_value[-4:] if len(field_value) >= 8 else '****'
                            else:
                                display_value = field_value
                            self.logger.info(f"✅ 配置准备: {field} = {display_value}")

            # 保存更新后的配置
            self.logger.info(f"💾 配置准备: 保存更新后的配置文件...")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 配置准备: 配置准备完成")
            return {'success': True, 'message': '配置准备完成'}

        except Exception as e:
            self.logger.error(f"❌ 配置准备: 准备配置时出错: {str(e)}")
            return {
                'success': False,
                'message': f'准备配置时出错: {str(e)}',
                'details': f'配置路径: {profile_dir}'
            }

    def _create_login_client(self, device: Dict[str, Any]) -> Optional['HuhhothomeLogin']:
        """创建并配置登录客户端"""
        try:
            self.logger.info(f"🌐 客户端创建: 开始创建登录客户端...")

            if not LOGIN_AVAILABLE:
                self.logger.error(f"❌ 客户端创建: Login模块不可用")
                return None

            # 优先使用代理管理器，如果可用的话
            if self.proxy_manager and self.proxy_manager.current_proxy:
                proxy_dict = self.proxy_manager.get_proxy_dict()
                self.logger.info(f"🔗 客户端创建: 使用代理管理器代理 {self.proxy_manager.current_proxy}")
                login_client = HuhhothomeLogin(proxy_config=proxy_dict, proxy_manager=self.proxy_manager, logger=self.logger)
            elif self.proxy_config:
                self.logger.info(f"🔗 客户端创建: 使用配置文件代理")
                proxy_info = f"{list(self.proxy_config.keys())[0]}://{list(self.proxy_config.values())[0]}" if self.proxy_config else "无"
                self.logger.info(f"📡 客户端创建: 代理信息 {proxy_info}")
                login_client = HuhhothomeLogin(proxy_config=self.proxy_config, logger=self.logger)
            else:
                self.logger.info(f"🔗 客户端创建: 使用直连模式创建客户端")
                login_client = HuhhothomeLogin(logger=self.logger)

            self.logger.info(f"✅ 客户端创建: HuhhothomeLogin客户端创建成功")

            # 设置Cookie
            cookie_str = device.get('cookie')
            access_token = device.get('access_token')

            if cookie_str and access_token:
                self.logger.info(f"🍪 客户端创建: 开始设置Cookie和Access Token...")

                # 解析Cookie字符串
                try:
                    cookie_dict = json.loads(cookie_str)
                    self.logger.info(f"✅ 客户端创建: Cookie解析成功，包含 {len(cookie_dict)} 个Cookie项")

                    # 设置Cookie到session
                    cookie_count = 0
                    for name, value in cookie_dict.items():
                        login_client.session.cookies.set(name, value)
                        cookie_count += 1
                        # 不记录Cookie的具体值，只记录名称
                        self.logger.info(f"🍪 客户端创建: 设置Cookie {name}")

                    self.logger.info(f"✅ 客户端创建: 成功设置 {cookie_count} 个Cookie")

                except json.JSONDecodeError as e:
                    self.logger.error(f"❌ 客户端创建: Cookie解析失败: {e}")
                    return None

                # 设置access token
                login_client.access_token = access_token
                login_client.headers["Membertoken"] = access_token

                # 脱敏显示Token
                token_display = access_token[:8] + "****" + access_token[-8:] if len(access_token) > 16 else "****"
                self.logger.info(f"🔑 客户端创建: Access Token设置成功 {token_display}")

            else:
                self.logger.warning(f"⚠️ 客户端创建: Cookie或Access Token缺失，客户端可能无法正常工作")

            self.logger.info(f"✅ 客户端创建: 登录客户端创建并配置完成")
            return login_client

        except Exception as e:
            self.logger.error(f"❌ 客户端创建: 创建登录客户端时出错: {e}")
            return None

    async def _run_grab_process(self, login_client: 'HuhhothomeLogin', profile_dir: str, house_id: str) -> Dict[str, Any]:
        """运行抢房流程"""
        try:
            # 使用asyncio在executor中运行同步的抢房流程
            loop = asyncio.get_event_loop()

            # 优化的grab_wrapper，增强错误信息提取能力
            def grab_wrapper():
                import io
                import sys
                from contextlib import redirect_stdout, redirect_stderr

                # 捕获标准输出和错误输出
                stdout_capture = io.StringIO()
                stderr_capture = io.StringIO()
                result = None
                output = ""
                error_output = ""
                submit_result = None

                try:
                    # 仅在调试模式下保存额外的启动信息
                    if self.enable_debug:
                        debug_info = {
                            'timestamp': datetime.now().isoformat(),
                            'house_id': house_id,
                            'profile_dir': profile_dir,
                            'proxy_config': self.proxy_config,
                            'device_username': login_client.__dict__.get('username', '未知'),
                            'start_time': datetime.now().isoformat()
                        }

                    # 修改抢房流程以返回详细结果
                    try:
                        with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                            # 调用login.py中的抢房流程，但同时捕获submit_application的结果
                            result = run_application_for_profile(login_client, profile_dir, house_id)
                    except Exception as inner_e:
                        # 如果抢房流程本身抛出异常，记录但不重新抛出
                        self.logger.error(f"抢房流程内部异常: {str(inner_e)}")
                        result = False

                    # 分析输出内容和返回值
                    output = stdout_capture.getvalue()
                    error_output = stderr_capture.getvalue()
                    full_output = output + error_output

                    # 仅在调试模式下更新和保存完整的调试信息
                    if self.enable_debug:
                        debug_info.update({
                            'end_time': datetime.now().isoformat(),
                            'stdout_output': output,
                            'stderr_output': error_output,
                            'full_output': full_output,
                            'function_result': result,
                            'result_type': type(result).__name__,
                            'result_judgment_method': 'function_return_value_enhanced'
                        })
                        self._save_debug_data(debug_info, house_id)

                    self.logger.info(f"📊 结果分析: 使用新的详细结果处理机制")

                    # 新的详细结果处理逻辑
                    if isinstance(result, dict):
                        # 新的返回格式：详细结果字典
                        success = result.get('success', False)
                        message = result.get('message', '未知')
                        details = result.get('details', '')

                        if success:
                            self.logger.info(f"✅ 结果分析: 抢房流程成功。消息: '{message}'")
                            return {
                                'success': True,
                                'message': message,
                                'details': details or '房源申请已成功提交，请稍后查看材料审核状态'
                            }
                        else:
                            self.logger.warning(f"❌ 结果分析: 抢房流程失败。具体错误: '{message}'")
                            return {
                                'success': False,
                                'message': message,
                                'details': details or f'房源申请提交失败: {message}'
                            }
                    elif result is True:
                        # 兼容旧的布尔值返回格式
                        self.logger.info(f"✅ 结果分析: 抢房流程成功（布尔值格式）")
                        return {
                            'success': True,
                            'message': '抢房申请提交成功',
                            'details': '房源申请已成功提交，请稍后查看材料审核状态'
                        }
                    elif result is False:
                        # 兼容旧的布尔值返回格式 - 仍使用增强的错误提取作为备用
                        error_message = self._extract_error_message_from_output_enhanced(full_output)
                        self.logger.warning(f"❌ 结果分析: 抢房流程失败（布尔值格式）。备用错误提取: '{error_message}'")

                        if error_message:
                            return {
                                'success': False,
                                'message': error_message,
                                'details': f'房源申请提交失败: {error_message}'
                            }
                        else:
                            return {
                                'success': False,
                                'message': '抢房申请提交失败',
                                'details': '房源申请提交过程中出现错误，无法获取具体原因'
                            }
                    else:
                        # 处理其他意外返回值
                        self.logger.warning(f"⚠️ 结果分析: 抢房流程返回了意外的结果类型: {type(result)}, 值: {result}")
                        if result:
                            return {
                                'success': True,
                                'message': '抢房申请可能成功',
                                'details': f'抢房流程返回了非标准成功值: {result}'
                            }
                        else:
                            error_message = self._extract_error_message_from_output_enhanced(full_output)
                            self.logger.warning(f"❌ 结果分析: 返回值为假值，判断为失败。备用错误提取: '{error_message}'")

                            if error_message:
                                return {
                                    'success': False,
                                    'message': error_message,
                                    'details': f'抢房流程返回失败值，具体原因: {error_message}'
                                }
                            else:
                                return {
                                    'success': False,
                                    'message': '抢房申请失败',
                                    'details': f'抢房流程返回了非标准失败值: {result}'
                                }

                except Exception as e:
                    self.logger.error(f"抢房流程(grab_wrapper)中出现异常", context=e)
                    # 即使出现异常，也尝试从已捕获的输出中提取信息
                    output = stdout_capture.getvalue()
                    error_output = stderr_capture.getvalue()
                    error_message = self._extract_error_message_from_output_enhanced(output + error_output)
                    return {
                        'success': False,
                        'message': f'抢房流程异常: {str(e)}',
                        'details': f'提取到的可能原因: {error_message}' if error_message else f'异常类型: {type(e).__name__}'
                    }

            # 在线程池中运行同步代码
            result = await loop.run_in_executor(None, grab_wrapper)
            return result

        except Exception as e:
            self.logger.error(f"运行抢房流程(_run_grab_process)时出错", context=e)
            return {
                'success': False,
                'message': f'运行抢房流程时出错: {str(e)}',
                'details': f'profile_dir: {profile_dir}, house_id: {house_id}'
            }

    def _save_debug_data(self, debug_info: Dict[str, Any], house_id: str):
        """保存调试数据到文件（仅在调试模式下执行）"""
        # 只有启用调试模式时才保存调试数据
        if not self.enable_debug or not self.debug_dir:
            return

        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            debug_filename = f"grab_debug_{house_id}_{timestamp}.json"
            debug_path = os.path.join(self.debug_dir, debug_filename)

            # 尝试读取配置文件中的提交数据
            profile_dir = debug_info.get('profile_dir', '')
            if profile_dir:
                debug_application_path = os.path.join(profile_dir, 'debug_application_data.json')
                if os.path.exists(debug_application_path):
                    try:
                        with open(debug_application_path, 'r', encoding='utf-8') as f:
                            application_data = json.load(f)
                        debug_info['submitted_application_data'] = application_data
                    except Exception as e:
                        debug_info['application_data_error'] = str(e)

            # 保存完整的调试信息
            with open(debug_path, 'w', encoding='utf-8') as f:
                json.dump(debug_info, f, ensure_ascii=False, indent=2)

            self.logger.info(f"调试数据已保存到: {debug_path}")

        except Exception as e:
            self.logger.error(f"保存调试数据时出错: {e}")

    def _extract_error_message_from_output(self, output: str) -> str:
        """
        从输出中提取具体的错误信息（仅用于错误信息提取，不用于成功/失败判断）

        Args:
            output: 捕获的标准输出和错误输出

        Returns:
            提取到的具体错误信息，如果没有找到则返回空字符串
        """
        if not output:
            return ""

        try:
            # 定义错误模式，按优先级排序（越具体的错误越优先）
            # 模式可以是字符串（完全匹配）或编译的正则表达式对象
            error_patterns = [
                # 1. 完全匹配的、最具体的高优先级错误
                '您的账号暂没有实名认证，或实名认证未通过！',
                '实名认证未通过',
                '暂没有实名认证',
                'SSL证书验证失败',
                '网络连接失败',
                '服务器错误',
                '连接超时',

                # 2. 带捕获组的、用于提取具体原因的模式
                re.compile(r'(?:申请|提交)失败[:：]\s*(.+)'),
                re.compile(r'失败原因[:：]\s*(.+)'),
                re.compile(r'登录失败[:：]\s*(.+)'),
                re.compile(r'选择房源失败[:：]\s*(.+)'),
                re.compile(r'上传文件失败[:：]\s*(.+)'),
                re.compile(r'Exception: (.+)'),

                # 3. 匹配一般错误日志行的模式
                re.compile(r'ERROR - (.+)'),
                re.compile(r'CRITICAL - (.+)'),
                re.compile(r"为 '.+?' 申请房源失败。"),
            ]

            self.logger.debug(f"正在从输出中提取错误信息（长度: {len(output)} 字符）")

            # 从后往前按行扫描，因为最后出现的错误通常是根本原因
            lines = output.strip().split('\n')
            for line in reversed(lines):
                line = line.strip()
                if not line:
                    continue

                for pattern in error_patterns:
                    error_message = ""
                    if isinstance(pattern, str):
                        if pattern in line:
                            error_message = pattern
                    else: # it's a compiled regex
                        match = pattern.search(line)
                        if match:
                            if pattern.groups > 0:
                                # 如果有捕获组，取第一个
                                error_message = match.group(1).strip()
                            else:
                                # 如果没有，取整个匹配
                                error_message = match.group(0).strip()

                    if error_message:
                        # 清理可能残留的日志头部信息
                        cleaned_message = re.sub(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}\s*-\s*\w+\s*-\s*(?:ERROR|CRITICAL)\s*-\s*', '', error_message).strip()
                        # 清理末尾的修饰性字符
                        cleaned_message = cleaned_message.strip("。=").strip()

                        if cleaned_message:
                            self.logger.info(f"提取到错误信息: '{cleaned_message}' (来自模式: '{pattern.pattern if hasattr(pattern, 'pattern') else pattern}')")
                            return cleaned_message

            self.logger.debug("未能从主要模式中提取到错误。将使用通用回退逻辑。")

            # 通用回退逻辑：如果以上模式都未匹配，查找任何包含"失败"或"错误"的行
            for line in reversed(lines):
                line = line.strip()
                if ('失败' in line or '错误' in line) and len(line) < 200:
                    cleaned_line = re.sub(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}\s*-\s*[\w\s\[\]]+\s*-\s*', '', line).strip()
                    cleaned_line = cleaned_line.strip("[]").strip()
                    if cleaned_line:
                        self.logger.info(f"从通用回退逻辑提取到错误: '{cleaned_line}'")
                        return cleaned_line

            self.logger.debug("未能从输出中提取到任何具体错误信息")
            return ""

        except Exception as e:
            self.logger.warning(f"提取错误信息时出现异常: {e}")
            return ""

    def _extract_error_message_from_output_enhanced(self, output: str) -> str:
        """
        增强版错误信息提取方法，更准确地识别和提取错误信息

        Args:
            output: 捕获的标准输出和错误输出

        Returns:
            提取到的具体错误信息，如果没有找到则返回空字符串
        """
        if not output:
            return ""

        try:
            self.logger.debug(f"使用增强版错误提取器处理输出（长度: {len(output)} 字符）")

            # 增强的错误模式，包含更多变体和更精确的匹配
            error_patterns = [
                # === 第一优先级：完全匹配的关键错误信息 ===
                '您的账号暂没有实名认证，或实名认证未通过！',
                '您的账号暂没有实名认证，或实名认证未通过',
                '实名认证未通过！',
                '实名认证未通过',
                '暂没有实名认证！',
                '暂没有实名认证',
                '账号未实名认证',
                '房源已被其他用户申请',
                '申请时间已过',
                '申请条件不符',
                '材料审核未通过',
                '余额不足',
                '账号异常',

                # === 第二优先级：网络和系统错误 ===
                'SSL证书验证失败',
                '网络连接失败',
                '服务器错误',
                '连接超时',
                '请求超时',
                '服务器响应异常',

                # === 第三优先级：带冒号的错误信息提取 ===
                re.compile(r'提交申请失败[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),
                re.compile(r'申请失败[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),
                re.compile(r'失败原因[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),
                re.compile(r'错误信息[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),
                re.compile(r'登录失败[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),
                re.compile(r'选择房源失败[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),
                re.compile(r'上传文件失败[:：]\s*(.+?)(?:\s|$)', re.IGNORECASE),

                # === 第四优先级：日志级别的错误信息 ===
                re.compile(r'ERROR\s*-\s*(.+)', re.IGNORECASE),
                re.compile(r'CRITICAL\s*-\s*(.+)', re.IGNORECASE),
                re.compile(r'FATAL\s*-\s*(.+)', re.IGNORECASE),

                # === 第五优先级：通用错误模式 ===
                re.compile(r'Exception[:：]?\s*(.+)', re.IGNORECASE),
                re.compile(r'Error[:：]?\s*(.+)', re.IGNORECASE),
                re.compile(r"为\s*['\"']?([^'\"']+)['\"']?\s*申请房源失败"),
            ]

            lines = output.strip().split('\n')

            # 首先尝试精确匹配高优先级错误
            for line in reversed(lines):
                line = line.strip()
                if not line:
                    continue

                # 检查是否包含实名认证相关的错误（最高优先级）
                if '实名认证' in line:
                    for pattern in error_patterns[:10]:  # 前10个是最高优先级的
                        if isinstance(pattern, str) and pattern in line:
                            self.logger.info(f"提取到高优先级错误: '{pattern}'")
                            return pattern

                # 通用模式匹配
                for pattern in error_patterns:
                    error_message = ""
                    if isinstance(pattern, str):
                        if pattern in line:
                            error_message = pattern
                    else:  # compiled regex
                        match = pattern.search(line)
                        if match:
                            if pattern.groups > 0:
                                error_message = match.group(1).strip()
                            else:
                                error_message = match.group(0).strip()

                    if error_message:
                        # 清理错误信息
                        cleaned_message = self._clean_error_message(error_message)
                        if cleaned_message:
                            pattern_desc = pattern.pattern if hasattr(pattern, 'pattern') else str(pattern)
                            self.logger.info(f"增强提取器找到错误: '{cleaned_message}' (模式: {pattern_desc})")
                            return cleaned_message

            # 高级回退策略：查找包含关键错误词汇的行
            error_keywords = ['失败', '错误', '异常', '超时', '拒绝', '无效', '不存在', '过期']
            for line in reversed(lines):
                line = line.strip()
                if any(keyword in line for keyword in error_keywords) and len(line) < 300:
                    # 清理日志前缀
                    cleaned_line = re.sub(r'^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,\.]\d{3}\s*-\s*[\w\s\[\]]+\s*-\s*(?:ERROR|WARNING|CRITICAL|INFO|DEBUG)\s*-\s*', '', line, flags=re.IGNORECASE).strip()
                    cleaned_line = self._clean_error_message(cleaned_line)

                    if cleaned_line and len(cleaned_line) > 3:  # 确保不是太短的无意义信息
                        self.logger.info(f"回退策略提取到错误: '{cleaned_line}'")
                        return cleaned_line

            self.logger.debug("增强提取器未能找到任何错误信息")
            return ""

        except Exception as e:
            self.logger.warning(f"增强错误提取器出现异常: {e}")
            # 回退到原始方法
            return self._extract_error_message_from_output(output)

    def _clean_error_message(self, message: str) -> str:
        """
        清理错误信息，移除不必要的前缀和后缀

        Args:
            message: 原始错误信息

        Returns:
            清理后的错误信息
        """
        if not message:
            return ""

        # 移除常见的日志前缀
        cleaned = re.sub(r'^[\[\]()（）]*\s*', '', message)
        cleaned = re.sub(r'\s*[\[\]()（）]*$', '', cleaned)

        # 移除时间戳前缀
        cleaned = re.sub(r'^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,\.]\d{3}\s*', '', cleaned)

        # 移除日志级别前缀
        cleaned = re.sub(r'^(?:DEBUG|INFO|WARNING|ERROR|CRITICAL)\s*[-:：]\s*', '', cleaned, flags=re.IGNORECASE)

        # 移除模块名前缀
        cleaned = re.sub(r'^[\w\[\]]+\s*[-:：]\s*', '', cleaned)

        # 移除多余的空白和标点
        cleaned = cleaned.strip('。！!.').strip()

        # 如果消息太短或只包含标点，返回空
        if len(cleaned) < 3 or cleaned.strip('.,!?，。！？') == '':
            return ""

        return cleaned