# Git相关
.git
.gitignore
.gitattributes

# 文档
README.md
*.md
docs/

# Docker相关
Dockerfile
docker-compose.yml
.dockerignore

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 测试相关
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 日志文件
*.log
log/
logs/

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 备份文件
*.bak
*.backup
backup/

# 数据库文件 (根据需要调整)
# *.db
# *.sqlite
# *.sqlite3

# 配置文件 (敏感信息)
.env
.env.local
.env.*.local

# 其他
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
